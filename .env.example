# EigenFit Foot Measurement API Configuration
# Copy this file to .env and modify as needed

# Application Settings
APP_NAME=EigenFit Foot-Measurement API
APP_VERSION=1.0.0
DEBUG=false

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Model Configuration
SAM_MODEL_PATH=./models/sam_vit_h_4b8939.pth
DINO_MODEL_ID=IDEA-Research/grounding-dino-base
SAM_MODEL_TYPE=vit_h

# Processing Configuration
BOX_THRESHOLD=0.4
TEXT_THRESHOLD=0.3
MULTIMASK_OUTPUT=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_EXTENSIONS=jpg,jpeg,png,webp
MAX_FILES_PER_REQUEST=10

# Security Configuration
CORS_ORIGINS=*
CORS_METHODS=GET,POST,OPTIONS
CORS_HEADERS=*

# Performance Configuration
TORCH_DEVICE=auto  # auto, cpu, cuda
TORCH_THREADS=4

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

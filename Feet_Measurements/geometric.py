import numpy as np
import cv2
from typing import <PERSON>ple
from Feet_Measurements.utils import GeometryUtils

class PageDetector:
    """
    Further refines the page contour using color thresholding and edge detection.
    """
    def detect_contour(self, roi: np.ndarray) -> np.ndarray:
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        mask = cv2.inRange(hsv, (0, 0, 125), (180, 30, 255))
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
        mask_closed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_close)
        edges = cv2.Canny(mask_closed, 100, 150)

        kernel_close2 = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close2)

        contours, _ = cv2.findContours(edges_closed, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise RuntimeError("No contours found in page detection")

        # Select the largest contour
        largest = max(contours, key=cv2.contourArea)
        return largest


class QuadrilateralProcessor:
    """Handles quadrilateral approximation and refinement"""
    def __init__(self, aspect_ratio_range: Tuple[float, float] = (1.3, 1.5)):
        self.aspect_ratio_range = aspect_ratio_range

    def get_quadrilateral(self, pts: np.ndarray) -> np.ndarray:
        """Find best quadrilateral approximation with aspect ratio validation"""
        pts2d = pts.reshape(-1, 2)
        hull = cv2.convexHull(pts2d)
        peri = cv2.arcLength(hull, True)
        
        # Binary search for optimal epsilon
        low, high = int(0.001 * peri), int(0.1 * peri)
        best_quad, best_area = None, 0
        
        while low <= high:
            eps = (low + high) // 2
            approx = cv2.approxPolyDP(hull, eps, True)
            if len(approx) == 4:
                area = cv2.contourArea(approx)
                if area > best_area:
                    best_area = area
                    best_quad = approx.reshape(-1, 2)
                low = eps + 1
            else:
                high = eps - 1
        
        # Validate aspect ratio
        if best_quad is not None:
            src = GeometryUtils.order_points(best_quad)
            width = np.linalg.norm(src[1] - src[0])
            height = np.linalg.norm(src[2] - src[1])
            if not (self.aspect_ratio_range[0] < height/width < self.aspect_ratio_range[1]):
                best_quad = None
        
        # Fallback with aspect ratio correction
        if best_quad is None:
            xs, ys = pts2d[:, 0], pts2d[:, 1]
            best_quad = np.array([
                [xs.min(), ys.min()],
                [xs.max(), ys.min()],
                [xs.max(), ys.max()],
                [xs.min(), ys.max()]
            ], dtype=np.float32)
            
            # Adjust to A4 aspect ratio
            width = best_quad[1][0] - best_quad[0][0]
            best_quad[2][1] = best_quad[3][1] = best_quad[0][1] + width * 297/210
            
        return best_quad

    def refine_quad_approximation(self, quad: np.ndarray) -> Tuple:
        """Refine quadrilateral using geometric properties"""
        edges = [(quad[i], quad[(i+1)%4]) for i in range(4)]
        
        # Compute edge vectors and lengths
        edge_data = []
        for i, (A, B) in enumerate(edges):
            v = B - A
            length = np.hypot(v[0], v[1])
            u = v / (length + 1e-8)
            edge_data.append({'idx': i, 'A': A, 'B': B, 'length': length, 'u': u})
        
        # Sort by length and find perpendicular pair
        edge_data.sort(key=lambda x: x['length'], reverse=True)
        longest = edge_data[0]
        candidates = edge_data[1:]
        perp = min(candidates, key=lambda e: abs(np.dot(e['u'], longest['u'])))
        
        # Find common vertex
        common_pt = np.array(list(set(map(tuple, [longest['A'], longest['B']]))
                             & set(map(tuple, [perp['A'], perp['B']])))).squeeze()
        
        # Compute fourth corner
        P_long = longest['B'] if np.array_equal(longest['A'], common_pt) else longest['A']
        P_perp = perp['B'] if np.array_equal(perp['A'], common_pt) else perp['A']
        corner4 = P_long + (P_perp - common_pt)
        
        # Construct refined quadrilateral
        rect = np.array([common_pt, P_long, corner4, P_perp], dtype=np.float32)
        return rect, 297/np.linalg.norm(P_long - common_pt), 210/np.linalg.norm(P_perp - common_pt)

from typing import Tuple, Optional, Dict, List
from segment_anything import sam_model_registry, SamPredictor
from transformers import AutoProcessor, AutoModelForZeroShotObjectDetection
import numpy as np
import torch
import cv2
from Feet_Measurements.utils import GeometryUtils
import logging
logger = logging.getLogger(__name__)
from Feet_Measurements.geometric import QuadrilateralProcessor

class ModelLoader:
    """Centralized model loading and management"""
    def __init__(self, 
                 dino_model_id: str = "IDEA-Research/grounding-dino-base",
                 sam_model_type: str = "vit_h",
                 sam_checkpoint_path: str = "sam_vit_h_4b8939.pth"):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.dino_processor, self.dino_model = self._load_dino(dino_model_id)
        self.sam_predictor = self._load_sam(sam_model_type, sam_checkpoint_path)
        
    def _load_dino(self, model_id: str) -> Tuple:
        """Load Grounding DINO model and processor"""
        processor = AutoProcessor.from_pretrained(model_id)
        model = AutoModelForZeroShotObjectDetection.from_pretrained(model_id).to(self.device)
        return processor, model
        
    def _load_sam(self, model_type: str, checkpoint_path: str) -> SamPredictor:
        """Load SAM model and predictor"""
        sam = sam_model_registry[model_type](checkpoint=checkpoint_path).to(self.device)
        return SamPredictor(sam) 

class FootMeasurementSystem:
    """Main system for foot measurement processing"""
    def __init__(self, model_loader: ModelLoader):
        self.model_loader = model_loader
        self.text_prompt = "a foot. a paper."
        self.quad_processor = QuadrilateralProcessor()

    def get_masks(self, image: np.ndarray) -> Tuple[torch.Tensor, torch.Tensor]:
        """Get masks for page and foot using Grounding DINO and SAM"""
        inputs = self.model_loader.dino_processor(
            images=image, 
            text=self.text_prompt, 
            return_tensors="pt"
        ).to(self.model_loader.device)
        
        # Handle mixed precision
        # for k, v in inputs.items():
        #     if v.dtype.is_floating_point:
        #         inputs[k] = v.half() if 'cuda' in self.model_loader.device else v
        
        with torch.no_grad():
            outputs = self.model_loader.dino_model(**inputs)
        
        # Process detections
        results = self.model_loader.dino_processor.post_process_grounded_object_detection(
            outputs,
            inputs.input_ids,
            box_threshold=0.4,
            text_threshold=0.3,
            target_sizes=[image.shape[:2]]
        )
        boxes = results[0]['boxes']
        
        # Generate masks with SAM
        self.model_loader.sam_predictor.set_image(image)
        transformed_boxes = self.model_loader.sam_predictor.transform.apply_boxes_torch(
            boxes, image.shape[:2]
        ).to(self.model_loader.device)
        
        masks, _, _ = self.model_loader.sam_predictor.predict_torch(
            point_coords=None,
            point_labels=None,
            boxes=transformed_boxes,
            multimask_output=False,
        )
        return masks[0], masks[1]

    @staticmethod
    def get_contour_from_mask(mask: torch.Tensor) -> np.ndarray:
        """Extract largest contour from mask tensor"""
        mask_np = mask[0].byte().cpu().numpy() * 255
        contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return max(contours, key=cv2.contourArea) if contours else None

    def min_gap_to_edge(self, contour_pts: np.ndarray, a: np.ndarray, b: np.ndarray, 
                        n: np.ndarray, u: np.ndarray, min_depth: float = 0, 
                        side_buffer: float = 0) -> Optional[Dict]:
        """Calculate minimal perpendicular distance to edge"""
        v = contour_pts - a
        s = v @ u
        d = v @ n
        
        # Vectorized filtering
        seg_len = np.linalg.norm(b - a)
        mask = (s >= side_buffer) & (s <= seg_len - side_buffer) & (d >= min_depth)
        
        if not np.any(mask):
            return None
            
        valid_d = d[mask]
        min_idx = np.argmin(valid_d)
        return {
            'gap': valid_d[min_idx],
            'edge_pt': a + s[mask][min_idx] * u,
            'contour_pt': contour_pts[mask][min_idx]
        }

    def compute_quad_contour_top_diff(self, quad: np.ndarray, contour: np.ndarray) -> Optional[Dict]:
        """Compute gap between quadrilateral top edge and contour"""
        quad_pts = GeometryUtils.sort_quad_corners(quad)
        centroid = np.mean(quad_pts, axis=0)
        u, n = GeometryUtils.edge_unit_vectors(quad_pts[0], quad_pts[1], centroid)
        return self.min_gap_to_edge(contour, quad_pts[0], quad_pts[1], n, u)

    def find_max_widths(self, quad: np.ndarray, contour: np.ndarray, 
                       bottom_buffer: float = 0.2, num_steps: int = 100) -> Tuple:
        """Find maximum widths within the contour"""
        ys, xs = contour[:, 1], contour[:, 0]
        y_top, y_bot = quad[:, 1].min(), quad[:, 1].max()
        y_fracs = np.linspace(0, 1 - bottom_buffer, num_steps)
        
        best_left = best_right = None
        best_left_dist = best_right_dist = np.inf
        
        for frac in y_fracs:
            y = y_top + frac * (y_bot - y_top)
            xl, xr = self.get_boundary_xs_at_y(quad, y)
            
            # Vectorized contour filtering
            tol_mask = np.abs(ys - y) <= 1
            if not np.any(tol_mask):
                continue
                
            xs_near = xs[tol_mask]
            xc_min, xc_max = xs_near.min(), xs_near.max()
            
            # Update best distances
            left_dist = xc_min - xl
            if left_dist < best_left_dist:
                best_left_dist = left_dist
                best_left = (xc_min, y)
                
            right_dist = xr - xc_max
            if right_dist < best_right_dist:
                best_right_dist = right_dist
                best_right = (xc_max, y)
                
        return best_left, best_right, best_left_dist + best_right_dist

    @staticmethod
    def get_boundary_xs_at_y(quad: np.ndarray, y: float) -> List[float]:
        """Get x-intercepts of quadrilateral at given y-level"""
        intersections = []
        for i in range(4):
            x1, y1 = quad[i]
            x2, y2 = quad[(i+1) % 4]
            if min(y1, y2) <= y <= max(y1, y2) and y1 != y2:
                t = (y - y1) / (y2 - y1)
                intersections.append(x1 + t * (x2 - x1))
        return sorted(intersections)[:2]

    def process_image(self, image: np.ndarray) -> Tuple[float, float]:
        """Main processing pipeline for foot measurement"""
        # Get masks and contours
        page_mask, foot_mask = self.get_masks(image)
        page_contour = self.get_contour_from_mask(page_mask)
        foot_contour = self.get_contour_from_mask(foot_mask)
        
        if page_contour is None or foot_contour is None:
            logger.error("Contour detection failed")
            return None, None
        
        # Process quadrilateral
        page_quad = self.quad_processor.get_quadrilateral(page_contour)
        refined_quad, scale_y, scale_x = self.quad_processor.refine_quad_approximation(page_quad)
        
        # Calculate foot dimensions
        gap_info = self.compute_quad_contour_top_diff(refined_quad, foot_contour.squeeze(1))
        if not gap_info:
            logger.warning("No valid contour points for gap measurement")
            return None, None
            
        _, _, width = self.find_max_widths(refined_quad, foot_contour.squeeze(1), 0.35, 500)
        
        foot_length = 297 - (gap_info['gap'] * scale_y)
        foot_width = 210 - (width * scale_x)
        
        return foot_length, foot_width






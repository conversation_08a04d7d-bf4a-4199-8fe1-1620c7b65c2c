import cv2
import numpy as np
from typing import Tuple

class GeometryUtils:
    """Geometric operations and calculations"""
    @staticmethod
    def order_points(pts: np.ndarray) -> np.ndarray:
        """Order 4 points as top-left, top-right, bottom-right, bottom-left"""
        rect = np.zeros((4, 2), dtype=np.float32)
        s = pts.sum(axis=1)
        diff = np.diff(pts, axis=1)
        rect[0] = pts[np.argmin(s)]
        rect[2] = pts[np.argmax(s)]
        rect[1] = pts[np.argmin(diff)]
        rect[3] = pts[np.argmax(diff)]
        return rect

    @staticmethod
    def sort_quad_corners(quad: np.ndarray) -> np.ndarray:
        """Orders quad corners in consistent order (tl, tr, br, bl)"""
        pts = quad.reshape(-1, 2)
        s = pts.sum(axis=1)
        diff = np.diff(pts, axis=1).ravel()
        tl = pts[np.argmin(s)]
        br = pts[np.argmax(s)]
        tr = pts[np.argmin(diff)]
        bl = pts[np.argmax(diff)]
        return np.array([tl, tr, br, bl], dtype=np.float32)

    @staticmethod
    def edge_unit_vectors(a: np.ndarray, b: np.ndarray, inward_point: np.ndarray) -> Tuple:
        """Returns unit edge vector and inward normal"""
        v = b - a
        u = v / (np.linalg.norm(v) + 1e-8)
        n = np.array([-u[1], u[0]])
        return u, n if np.dot((inward_point - a), n) >= 0 else -n






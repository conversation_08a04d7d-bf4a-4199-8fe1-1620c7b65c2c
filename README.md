# EigenFit Foot Measurement API

A sophisticated AI-powered foot measurement system that extracts precise physical dimensions from smartphone images using advanced computer vision and deep learning techniques.

## 🚀 Features

- **AI-Powered Measurement**: Uses Segment Anything Model (SAM) and Grounding DINO for precise foot segmentation
- **Multi-View Analysis**: Supports both top-view and side-view foot measurements
- **RESTful API**: FastAPI-based web service with automatic documentation
- **Docker Support**: Containerized deployment for easy scaling and deployment
- **Real-time Processing**: Efficient processing pipeline optimized for production use
- **Comprehensive Measurements**: Extracts length, width, and arch measurements

## 📋 Prerequisites

### System Requirements
- **Python**: 3.11 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: At least 5GB free space (for models and dependencies)
- **GPU**: CUDA-compatible GPU recommended (CPU fallback available)

### For Docker Deployment
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher

## 🛠️ Installation

### Option 1: Docker Deployment (Recommended)

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd eigenImageAnalysis
   ```

2. **Build and run with Docker Compose**:
   ```bash
   docker-compose up --build
   ```

3. **Access the API**:
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Option 2: Local Development Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd eigenImageAnalysis
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Download SAM model**:
   ```bash
   mkdir -p models
   wget -O models/sam_vit_h_4b8939.pth https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
   ```

5. **Run the application**:
   ```bash
   uvicorn app:app --host 0.0.0.0 --port 8000
   ```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and modify as needed:

```bash
cp .env.example .env
```

Key configuration options:
- `DEBUG`: Enable debug mode (default: false)
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `SAM_MODEL_PATH`: Path to SAM model file
- `LOG_LEVEL`: Logging level (INFO, DEBUG, WARNING, ERROR)

## 📖 Usage

### API Endpoints

#### Health Check
```bash
GET /health
```

#### Root Information
```bash
GET /
```

#### Foot Measurement
```bash
POST /measure
Content-Type: multipart/form-data

# Upload one or more images
curl -X POST "http://localhost:8000/measure" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "images=@foot_image1.jpg" \
     -F "images=@foot_image2.jpg"
```

### Response Format

```json
{
  "status": "completed",
  "processed_images": 2,
  "measurements": [
    {
      "image_index": 0,
      "filename": "foot_image1.jpg",
      "length_mm": 245.67,
      "width_mm": 89.23,
      "status": "success"
    },
    {
      "image_index": 1,
      "filename": "foot_image2.jpg",
      "length_mm": 248.12,
      "width_mm": 91.45,
      "status": "success"
    }
  ]
}
```

### Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)

### Image Requirements

- **Reference Object**: Include an A4 paper sheet in the image for scale calibration
- **Foot Positioning**: Place foot clearly on the A4 paper
- **Lighting**: Ensure good lighting conditions
- **Resolution**: Minimum 1536x2048 pixels recommended
- **File Size**: Maximum 10MB per image

## 🏗️ Project Structure

```
eigenImageAnalysis/
├── app.py                      # FastAPI application
├── pipeline.py                 # Standalone processing script
├── demo.py                     # Demo processing pipeline
├── template.py                 # Multi-view analysis template
├── Feet_Measurements/          # Core measurement system
│   ├── image_processor.py      # Main processing logic
│   ├── geometric.py           # Geometric calculations
│   └── utils.py               # Utility functions
├── images/                     # Sample images
├── ExampleImages/             # Example datasets
├── models/                    # AI model files (created on first run)
├── dockerfile                 # Docker configuration
├── docker-compose.yml         # Docker Compose configuration
├── requirements.txt           # Python dependencies
├── .env.example              # Environment configuration template
└── README.md                 # This file
```

## 🧪 Testing

### Test the API

1. **Health Check**:
   ```bash
   curl http://localhost:8000/health
   ```

2. **Process Sample Images**:
   ```bash
   curl -X POST "http://localhost:8000/measure" \
        -H "Content-Type: multipart/form-data" \
        -F "images=@images/sample.webp"
   ```

### Run Standalone Pipeline

```bash
python pipeline.py
```

## 🐳 Docker Commands

### Build Image
```bash
docker build -t eigenfit-foot-measurement .
```

### Run Container
```bash
docker run -p 8000:8000 eigenfit-foot-measurement
```

### View Logs
```bash
docker-compose logs -f eigenfit-api
```

### Stop Services
```bash
docker-compose down
```

## 🔍 Troubleshooting

### Common Issues

1. **Model Download Fails**:
   - Check internet connection
   - Manually download SAM model to `models/` directory

2. **Out of Memory Errors**:
   - Reduce image resolution
   - Use CPU instead of GPU: set `TORCH_DEVICE=cpu`
   - Increase Docker memory limits

3. **Import Errors**:
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version compatibility

4. **Permission Errors (Docker)**:
   - Ensure Docker daemon is running
   - Check file permissions in project directory

### Performance Optimization

- **GPU Usage**: Ensure CUDA is properly installed for GPU acceleration
- **Memory**: Allocate sufficient memory (4GB minimum, 8GB recommended)
- **Batch Processing**: Process multiple images in single request for efficiency

## 📝 Development

### Adding New Features

1. Fork the repository
2. Create a feature branch
3. Implement changes
4. Add tests
5. Submit pull request

### Code Style

- Follow PEP 8 guidelines
- Use type hints
- Add docstrings for functions and classes
- Maintain test coverage

## 📄 License

[Add your license information here]

## 🤝 Contributing

[Add contribution guidelines here]

## 📞 Support

[Add support contact information here]

from fastapi import FastAPI, UploadFile, File, HTTPException
import tempfile, shutil, json, os
import cv2
import logging
from Feet_Measurements.image_processor import ModelLoader, FootMeasurementSystem

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="EigenFit Foot-Measurement API",
    description="API for measuring foot dimensions from smartphone images",
    version="1.0.0"
)

# Initialize model once at startup (singleton pattern)
model_loader = None
measurement_system = None

@app.on_event("startup")
async def startup_event():
    """Initialize the model on application startup."""
    global model_loader, measurement_system
    logger.info("Starting application startup process...")
    
    try:
        logger.info("Checking if SAM model file exists...")
        sam_model_path = "./models/sam_vit_h_4b8939.pth"
        
        if not os.path.exists(sam_model_path):
            logger.error(f"SAM model file not found at: {sam_model_path}")
            logger.info(f"Contents of models directory: {os.listdir('./models') if os.path.exists('./models') else 'Directory does not exist'}")
            raise FileNotFoundError(f"SAM model file not found: {sam_model_path}")
        
        logger.info(f"SAM model file found. Size: {os.path.getsize(sam_model_path) / (1024*1024*1024):.2f} GB")
        
        logger.info("Importing ModelLoader and FootMeasurementSystem...")
        from Feet_Measurements.image_processor import ModelLoader, FootMeasurementSystem
        logger.info("Import successful")
        
        logger.info("Initializing ModelLoader...")
        model_loader = ModelLoader(sam_checkpoint_path=sam_model_path)
        logger.info("ModelLoader initialized successfully")
        
        logger.info("Initializing FootMeasurementSystem...")
        measurement_system = FootMeasurementSystem(model_loader)
        logger.info("FootMeasurementSystem initialized successfully")
        
        logger.info("Model loaded successfully - Application ready!")
        
    except ImportError as e:
        logger.error(f"Import error during model loading: {e}")
        raise
    except FileNotFoundError as e:
        logger.error(f"File not found during model loading: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during model loading: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        raise


@app.get("/health")
async def health_check():
    """Health check endpoint for container monitoring."""
    return {"status": "healthy", "service": "eigenfit-foot-measurement"}

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "EigenFit Foot Measurement API",
        "version": "1.0.0",
        "endpoints": {
            "measure": "/measure - POST endpoint for foot measurement",
            "health": "/health - Health check endpoint"
        }
    }

@app.post("/measure")
async def measure(images: list[UploadFile] = File(...)):
    """
    Measure foot dimensions from uploaded images.
    Returns JSON with measurements for each foot.
    """
    if not images:
        raise HTTPException(status_code=400, detail="At least one image required")
    
    # Validate file types
    allowed_types = {"image/jpeg", "image/jpg", "image/png", "image/webp"}
    for img in images:
        if img.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {img.content_type}. Allowed: {allowed_types}"
            )
    
    # Process images
    tmp_dir = tempfile.mkdtemp()
    results = []
    
    try:
        logger.info(f"Processing {len(images)} images for foot measurement")
        
        for i, uploaded_file in enumerate(images):
            # Save uploaded file temporarily
            filename = f"image_{i}.{uploaded_file.filename.split('.')[-1]}" if uploaded_file.filename else f"image_{i}.jpg"
            file_path = os.path.join(tmp_dir, filename)
            
            with open(file_path, "wb") as out:
                content = await uploaded_file.read()
                out.write(content)
                logger.info(f"Saved image {i+1}: {filename} ({len(content)} bytes)")
            
            # Process the image
            try:
                image = cv2.imread(file_path)
                if image is None:
                    logger.warning(f"Could not read image: {filename}")
                    continue
                
                length, width = measurement_system.process_image(image)
                
                result = {
                    "image_index": i,
                    "filename": uploaded_file.filename or filename,
                    "length_mm": round(length, 2) if length else None,
                    "width_mm": round(width, 2) if width else None,
                    "status": "success" if length and width else "failed"
                }
                
                results.append(result)
                logger.info(f"Processed {filename}: Length = {length:.2f}mm, Width = {width:.2f}mm")
                
            except Exception as e:
                logger.error(f"Error processing {filename}: {e}")
                results.append({
                    "image_index": i,
                    "filename": uploaded_file.filename or filename,
                    "length_mm": None,
                    "width_mm": None,
                    "status": "error",
                    "error": str(e)
                })
        
        logger.info("Foot measurement processing completed")
        return {
            "status": "completed",
            "processed_images": len(results),
            "measurements": results
        }
        
    except Exception as e:
        logger.error(f"Error during foot measurement processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")
    
    finally:
        shutil.rmtree(tmp_dir, ignore_errors=True)
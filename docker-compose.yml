version: '3.8'

services:
  eigenfit-api:
    build:
      context: .
      dockerfile: dockerfile
    container_name: eigenfit-foot-measurement
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      # Mount models directory if you want to persist downloaded models
      - ./models:/app/models
      # Mount for development (comment out for production)
      # - .:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Resource limits (adjust based on your needs)
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # Optional: Add a reverse proxy for production
  # nginx:
  #   image: nginx:alpine
  #   container_name: eigenfit-nginx
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - eigenfit-api
  #   restart: unless-stopped

networks:
  default:
    name: eigenfit-network

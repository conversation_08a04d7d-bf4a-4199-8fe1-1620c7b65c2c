import os 
import cv2
from Feet_Measurements.image_processor import  Model<PERSON>oader, FootMeasurementSystem
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if __name__ == "__main__":

    logger.info("Loading model and initializing measurement system...")
    # Initialize once
    model_loader = ModelLoader(sam_checkpoint_path="./models/sam_vit_h_4b8939.pth")
    measurement_system = FootMeasurementSystem(model_loader)
    
    logger.info("Model Loaded Succesfully...")
    imgage_dir = "./images/"
    image_files = [os.path.join(imgage_dir, f) for f in os.listdir(imgage_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.webp'))]

    logger.info("Processing images...")
    if not image_files:
        logger.warning("No images found in the specified directory.")
        exit(1)
    # Process images
    for img_path in image_files:
        try:
            image = cv2.imread(img_path)
            if image is None:
                continue
                
            length, width = measurement_system.process_image(image)
            # print(f"Processed {img_path}: Length = {length:.2f}mm, Width = {width:.2f}mm")
            # if length and width:
            logger.info(f"Foot length: {length:.2f}mm, Width: {width:.2f}mm")
                
        except Exception as e:
            # print(f"Error processing {img_path}: {e}")
            logger.error(f"Error processing {img_path}")
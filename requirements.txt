# Core FastAPI and web server dependencies
fastapi==0.115.12
uvicorn[standard]==0.34.3
python-multipart==0.0.20
python-dotenv==1.1.0

# AI/ML Core Dependencies
torch==2.7.0
torchvision==0.22.0
torchaudio==2.7.0
transformers==4.52.4
huggingface-hub==0.32.3

# Computer Vision Dependencies
opencv-python==*********
pillow==11.2.1
numpy==2.2.6

# Segment Anything Model
segment_anything @ git+https://github.com/facebookresearch/segment-anything.git@dca509fe793f601edb92606367a655c15ac00fdf

# Data Processing
pydantic==2.11.5
pydantic_core==2.33.2

# Utilities
requests==2.32.3
PyYAML==6.0.2
tqdm==4.67.1
packaging==25.0

# Development and Logging
rich==14.0.0

# Security and validation
certifi==2025.4.26

# File handling
filelock==3.18.0
fsspec==2025.5.1
safetensors==0.5.3

# Text processing
tokenizers==0.21.1
regex==2024.11.6

# Math and scientific computing
sympy==1.14.0
mpmath==1.3.0
networkx==3.5

# Web framework dependencies
starlette==0.46.2
anyio==4.9.0
sniffio==1.3.1
h11==0.16.0
httptools==0.6.4
uvloop==0.21.0
watchfiles==1.0.5
websockets==15.0.1
click==8.2.1

# Template and markup
Jinja2==3.1.6
MarkupSafe==3.0.2
markdown-it-py==3.0.0
mdurl==0.1.2
Pygments==2.19.1

# Type annotations and validation
annotated-types==0.7.0
typing_extensions==4.13.2
typing-inspection==0.4.1

# HTTP clients and tools
httpx==0.28.1
httpcore==1.0.9
charset-normalizer==3.4.2
idna==3.10
urllib3==2.4.0

# CLI and development tools
typer==0.16.0
shellingham==1.5.4
rich-toolkit==0.14.7
fastapi-cli==0.0.7

# Additional utilities
dnspython==2.7.0
email_validator==2.2.0
hf-xet==1.1.2
setuptools==80.9.0

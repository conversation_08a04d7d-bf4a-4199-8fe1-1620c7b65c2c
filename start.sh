#!/bin/bash

# EigenFit Foot Measurement API Startup Script
# This script helps you get the application running quickly

set -e  # Exit on any error

echo "🚀 EigenFit Foot Measurement API Startup"
echo "========================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if Dock<PERSON> is running
docker_running() {
    docker info >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists python3; then
    echo "❌ Python 3 is not installed"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "✅ Python $PYTHON_VERSION found"

# Check if we should use Docker or local setup
USE_DOCKER=false
if command_exists docker && command_exists docker-compose; then
    echo "🐳 Docker and <PERSON><PERSON> Compose found"
    if docker_running; then
        echo "✅ Docker is running"
        USE_DOCKER=true
    else
        echo "⚠️  Docker is installed but not running"
        echo "Please start Docker and try again, or use local setup"
    fi
else
    echo "ℹ️  Docker not found, using local setup"
fi

# Ask user for preference
echo ""
echo "Choose deployment method:"
echo "1) Docker (recommended)"
echo "2) Local development setup"
echo "3) Run tests only"
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        if [ "$USE_DOCKER" = true ]; then
            echo ""
            echo "🐳 Starting with Docker..."
            echo "Building and starting containers..."
            docker-compose up --build
        else
            echo "❌ Docker is not available. Please install Docker or choose option 2."
            exit 1
        fi
        ;;
    2)
        echo ""
        echo "🔧 Setting up local development environment..."
        
        # Check if virtual environment exists
        if [ ! -d "venv" ]; then
            echo "Creating virtual environment..."
            python3 -m venv venv
        fi
        
        echo "Activating virtual environment..."
        source venv/bin/activate
        
        echo "Installing dependencies..."
        pip install --upgrade pip
        pip install -r requirements.txt
        
        # Check if models directory exists and download SAM model if needed
        if [ ! -d "models" ]; then
            mkdir -p models
        fi
        
        if [ ! -f "models/sam_vit_h_4b8939.pth" ]; then
            echo "Downloading SAM model (this may take a while)..."
            wget -O models/sam_vit_h_4b8939.pth https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
        else
            echo "✅ SAM model already exists"
        fi
        
        echo "Running setup tests..."
        python test_setup.py
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "🎉 Setup complete! Starting the application..."
            echo "API will be available at: http://localhost:8000"
            echo "API documentation: http://localhost:8000/docs"
            echo ""
            uvicorn app:app --host 0.0.0.0 --port 8000 --reload
        else
            echo "❌ Setup tests failed. Please check the errors above."
            exit 1
        fi
        ;;
    3)
        echo ""
        echo "🧪 Running setup tests..."
        python test_setup.py
        ;;
    *)
        echo "Invalid choice. Exiting."
        exit 1
        ;;
esac

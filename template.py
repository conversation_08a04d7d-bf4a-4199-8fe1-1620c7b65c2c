"""
EigenFit Foot Measurement – Python Template
==========================================

A **lean, implementation-ready skeleton** for extracting physical foot
measurements from **a specific set of four smartphone images**:

1. Left foot from top view
2. Right foot from top view
3. Left foot inner side view (for arch calculation)
4. Right foot inner side view (for arch calculation)

Key responsibilities
--------------------
1. **I/O & validation** – load the four images and verify they meet the agreed spec.
2. **Calibration** – detect the A4 sheet in *each* image to derive a per‑image
   millimetre‑per‑pixel scale.
3. **Foot detection & keypoints** – isolate the foot in every view and detect required
   keypoints based on the view type (top or inner side).
4. **Geometry** – compute physical measurements from keypoints appropriate to each view.
5. **Aggregation** – combine multi‑view data into a single JSON payload with complete
   measurements for both feet.
6. **Orientation Handling** - detect and handle both portrait and landscape orientations.

Project layout
-------------
```
├── eigenfit_image_analysis_template.py  # ← this file (imported as module)
├── app.py                               # FastAPI web service
├── dockerfile                           # Container definition
└── requirements.txt                     # Dependencies
```
"""

from __future__ import annotations

import json
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Sequence, Tuple

# Third‑party libraries – declare in requirements.txt
import cv2  # type: ignore
import numpy as np  # type: ignore

# Optional: Mediapipe can be handy for keypoint detection
try:
    import mediapipe as mp  # type: ignore
except ImportError:  # pragma: no cover
    mp = None

###############################################################################
# Configuration & logging                                                     #
###############################################################################

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)-8s | %(name)s | %(message)s",
)
logger = logging.getLogger("eigenfit")

# Physical constants
A4_WIDTH_MM: float = 210.0
A4_HEIGHT_MM: float = 297.0

# ---------------------------------------------------------------------------
# Standardised input‑image specification (guaranteed upstream)
# ---------------------------------------------------------------------------
STANDARD_LANDSCAPE_WIDTH_PX: int = 2048  # long side (landscape)
STANDARD_LANDSCAPE_HEIGHT_PX: int = 1536  # short side (landscape)
STANDARD_PORTRAIT_WIDTH_PX: int = 1536  # short side (portrait)
STANDARD_PORTRAIT_HEIGHT_PX: int = 2048  # long side (portrait)

# Expected number of images per session
NUM_EXPECTED_IMAGES: int = 4

# Image orientation
ORIENTATION_LANDSCAPE = "landscape"
ORIENTATION_PORTRAIT = "portrait"

###############################################################################
# Data classes                                                                 #
###############################################################################


@dataclass
class TopViewFootKeypoints:
    """Pixel coordinates of foot landmarks from top view (all in *px*)."""

    heel: Tuple[int, int]
    toe: Tuple[int, int]
    inner_width: Tuple[int, int]
    outer_width: Tuple[int, int]


@dataclass
class SideViewFootKeypoints:
    """Pixel coordinates of foot landmarks from inner side view (all in *px*)."""

    arch_back: Tuple[int, int]
    arch_front: Tuple[int, int]
    arch_low: Tuple[int, int]
    arch_high: Tuple[int, int]


@dataclass
class FootMeasurements:
    """Physical metrics in millimetres."""

    heel_to_toe_length: float
    foot_max_width: float
    arch_length: float
    arch_height: float

    def to_json_dict(self) -> Dict[str, float]:
        return {
            "heelToToeLength": self.heel_to_toe_length,
            "footMaxWidth": self.foot_max_width,
            "archLength": self.arch_length,
            "archHeight": self.arch_height,
        }


###############################################################################
# Core functional classes                                                     #
###############################################################################


class Calibrator:
    """Locate the A4 sheet and estimate the *mm per pixel* scale factor."""

    def __init__(self, img: np.ndarray, *, debug: bool = False) -> None:
        self.img = img
        self.debug = debug

    def find_paper_contour(self) -> np.ndarray:
        """Return the largest 4‑point contour that resembles A4 paper."""
        raise NotImplementedError

    def mm_per_px(self) -> float:
        """Compute millimetres‑per‑pixel scale using the detected contour."""
        raise NotImplementedError


class TopViewFootDetector:
    """Extract foot region and keypoints from top view images."""

    def __init__(self, img: np.ndarray, *, debug: bool = False) -> None:
        self.img = img
        self.debug = debug

    def keypoints(self) -> TopViewFootKeypoints:
        """Return ordered keypoints for the foot in this top view image."""
        raise NotImplementedError


class SideViewFootDetector:
    """Extract foot arch keypoints from inner side view images."""

    def __init__(self, img: np.ndarray, *, debug: bool = False) -> None:
        self.img = img
        self.debug = debug

    def keypoints(self) -> SideViewFootKeypoints:
        """Return ordered keypoints for the foot arch in this side view image."""
        raise NotImplementedError


class FootGeometry:
    """Convert *pixel* keypoints into *millimetre* measurements."""

    def __init__(self, mm_per_px: float):
        self.scale = mm_per_px

    @staticmethod
    def _distance(p1: Tuple[int, int], p2: Tuple[int, int]) -> float:
        return float(
            np.linalg.norm(np.array(p1, dtype=float) - np.array(p2, dtype=float))
        )

    def metrics_from_top_view(self, kp: TopViewFootKeypoints) -> Dict[str, float]:
        """Calculate length and width measurements from top view."""
        length_px = self._distance(kp.heel, kp.toe)
        width_px = self._distance(kp.inner_width, kp.outer_width)

        return {
            "heelToToeLength": length_px * self.scale,
            "footMaxWidth": width_px * self.scale,
        }

    def metrics_from_side_view(self, kp: SideViewFootKeypoints) -> Dict[str, float]:
        """Calculate arch measurements from side view."""
        arch_len_px = self._distance(kp.arch_back, kp.arch_front)
        arch_ht_px = self._distance(kp.arch_low, kp.arch_high)

        return {
            "archLength": arch_len_px * self.scale,
            "archHeight": arch_ht_px * self.scale,
        }


###############################################################################
# Multi‑image pipeline façade                                                 #
###############################################################################


class EigenFitSession:
    """High‑level API – process **four specific foot images** → unified JSON payload.

    The four images should be:
    1. Left foot from top view
    2. Right foot from top view
    3. Left foot inner side view (for arch calculation)
    4. Right foot inner side view (for arch calculation)
    """

    # Image indices for clarity
    LEFT_TOP_VIEW = 0
    RIGHT_TOP_VIEW = 1
    LEFT_SIDE_VIEW = 2
    RIGHT_SIDE_VIEW = 3

    def __init__(self, img_paths: Sequence[str | Path], *, debug: bool = False) -> None:
        if len(img_paths) != NUM_EXPECTED_IMAGES:
            raise ValueError(
                f"Expected {NUM_EXPECTED_IMAGES} images, got {len(img_paths)}"
            )
        self.paths = [Path(p) for p in img_paths]
        self.debug = debug
        self.imgs: List[np.ndarray] = [self._load_and_validate(p) for p in self.paths]

    # ------------------------------------------------------------------
    # Public methods
    # ------------------------------------------------------------------

    def run(self) -> Dict[str, Any]:
        """Process all four images and merge results into one JSON‑ready dict.

        The processing follows this pattern:
        1. Get length and width from top views (images 0 and 1)
        2. Get arch measurements from side views (images 2 and 3)
        3. Combine the measurements for each foot
        """
        # Initialize results structure
        result: Dict[str, Dict[str, Any]] = {"leftFoot": {}, "rightFoot": {}}

        # Process each image with its appropriate detector and measurements
        for img_idx, img in enumerate(self.imgs):
            # Normalize orientation before processing
            # Top view images are typically clearer in landscape, side view in portrait
            target_orientation = (
                ORIENTATION_LANDSCAPE
                if img_idx in (self.LEFT_TOP_VIEW, self.RIGHT_TOP_VIEW)
                else ORIENTATION_PORTRAIT
            )
            normalized_img = self.normalize_orientation(img, target_orientation)

            # For all images: calibrate to get mm per pixel scale
            calib = Calibrator(normalized_img, debug=self.debug)
            mm_per_px = calib.mm_per_px()
            geom = FootGeometry(mm_per_px)

            # Process based on image type
            if img_idx == self.LEFT_TOP_VIEW:
                # Left foot top view - get length and width
                detector = TopViewFootDetector(normalized_img, debug=self.debug)
                top_keypoints = detector.keypoints()
                result["leftFoot"].update(geom.metrics_from_top_view(top_keypoints))

            elif img_idx == self.RIGHT_TOP_VIEW:
                # Right foot top view - get length and width
                detector = TopViewFootDetector(normalized_img, debug=self.debug)
                top_keypoints = detector.keypoints()
                result["rightFoot"].update(geom.metrics_from_top_view(top_keypoints))

            elif img_idx == self.LEFT_SIDE_VIEW:
                # Left foot inner side view - get arch measurements
                detector = SideViewFootDetector(normalized_img, debug=self.debug)
                side_keypoints = detector.keypoints()
                result["leftFoot"].update(geom.metrics_from_side_view(side_keypoints))

            elif img_idx == self.RIGHT_SIDE_VIEW:
                # Right foot inner side view - get arch measurements
                detector = SideViewFootDetector(normalized_img, debug=self.debug)
                side_keypoints = detector.keypoints()
                result["rightFoot"].update(geom.metrics_from_side_view(side_keypoints))

        if self.debug:
            logger.debug("Result JSON: %s", json.dumps(result, indent=2))
        return result

    # ------------------------------------------------------------------
    # Private helpers
    # ------------------------------------------------------------------

    def _load_and_validate(self, path: Path) -> np.ndarray:
        if not path.exists():
            raise FileNotFoundError(path)
        img = cv2.imread(str(path))
        if img is None:
            raise ValueError(f"Could not read image: {path}")

        # Detect orientation
        h, w = img.shape[:2]
        orientation = self._detect_orientation(w, h)

        # Check dimensions based on orientation
        if orientation == ORIENTATION_LANDSCAPE:
            expected_w, expected_h = (
                STANDARD_LANDSCAPE_WIDTH_PX,
                STANDARD_LANDSCAPE_HEIGHT_PX,
            )
        else:  # portrait
            expected_w, expected_h = (
                STANDARD_PORTRAIT_WIDTH_PX,
                STANDARD_PORTRAIT_HEIGHT_PX,
            )

        if (w, h) != (expected_w, expected_h):
            logger.warning(
                "Image %s dims %sx%s px differ from standard %sx%s px for %s orientation",
                path.name,
                w,
                h,
                expected_w,
                expected_h,
                orientation,
            )

        return img

    @staticmethod
    def _detect_orientation(width: int, height: int) -> str:
        """Detect if an image is in portrait or landscape orientation."""
        if width > height:
            return ORIENTATION_LANDSCAPE
        return ORIENTATION_PORTRAIT

    def normalize_orientation(
        self, img: np.ndarray, target_orientation: str = ORIENTATION_LANDSCAPE
    ) -> np.ndarray:
        """Rotate the image if needed to match the target orientation.

        Args:
            img: The input image
            target_orientation: Desired orientation ('landscape' or 'portrait')

        Returns:
            Rotated image if needed, original image otherwise
        """
        h, w = img.shape[:2]
        current_orientation = self._detect_orientation(w, h)

        # If orientations already match, return original
        if current_orientation == target_orientation:
            return img

        # Otherwise, rotate 90 degrees to convert between landscape and portrait
        if self.debug:
            logger.info(
                "Rotating image from %s to %s", current_orientation, target_orientation
            )

        # Rotate 90 degrees
        # cv2 constants for rotation: 0=90deg, 1=180deg, 2=270deg
        return cv2.transpose(cv2.flip(img, 1))


###############################################################################
# Command‑line interface (demo)                                               #
###############################################################################


def _build_cli() -> None:  # pragma: no cover
    import argparse

    parser = argparse.ArgumentParser(description="EigenFit Foot Measurement")
    parser.add_argument(
        "images",
        nargs=4,
        help=(
            "Paths to four specific foot images:\n"
            "1. Left foot from top view\n"
            "2. Right foot from top view\n"
            "3. Left foot inner side view (for arch)\n"
            "4. Right foot inner side view (for arch)"
        ),
    )
    parser.add_argument("--debug", action="store_true", help="Verbose logging")
    args = parser.parse_args()

    session = EigenFitSession(args.images, debug=args.debug)
    print(json.dumps(session.run(), indent=2))


if __name__ == "__main__":  # pragma: no cover
    _build_cli()

#!/usr/bin/env python3
"""
Test script to verify the EigenFit setup and dependencies.
Run this script to check if all components are working correctly.
"""

import sys
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_python_version():
    """Test Python version compatibility."""
    logger.info("Testing Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        logger.info(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        logger.error(f"✗ Python {version.major}.{version.minor}.{version.micro} is not compatible. Requires Python 3.11+")
        return False

def test_dependencies():
    """Test if all required dependencies are available."""
    logger.info("Testing dependencies...")
    required_packages = [
        'fastapi',
        'uvicorn',
        'torch',
        'torchvision',
        'transformers',
        'cv2',
        'numpy',
        'PIL',
        'segment_anything'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                from PIL import Image
            else:
                __import__(package)
            logger.info(f"✓ {package} is available")
        except ImportError:
            logger.error(f"✗ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing packages: {', '.join(missing_packages)}")
        logger.error("Run: pip install -r requirements.txt")
        return False
    
    logger.info("✓ All dependencies are available")
    return True

def test_model_file():
    """Test if SAM model file exists."""
    logger.info("Testing SAM model file...")
    model_path = Path("./models/sam_vit_h_4b8939.pth")
    
    if model_path.exists():
        size_gb = model_path.stat().st_size / (1024**3)
        logger.info(f"✓ SAM model file found ({size_gb:.2f} GB)")
        return True
    else:
        logger.warning("✗ SAM model file not found")
        logger.info("The model will be downloaded automatically on first run")
        logger.info("Or download manually:")
        logger.info("mkdir -p models")
        logger.info("wget -O models/sam_vit_h_4b8939.pth https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth")
        return False

def test_directory_structure():
    """Test if required directories exist."""
    logger.info("Testing directory structure...")
    required_dirs = [
        "Feet_Measurements",
        "images"
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            logger.info(f"✓ {dir_name}/ directory exists")
        else:
            logger.error(f"✗ {dir_name}/ directory missing")
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        logger.error(f"Missing directories: {', '.join(missing_dirs)}")
        return False
    
    logger.info("✓ Directory structure is correct")
    return True

def test_core_modules():
    """Test if core modules can be imported."""
    logger.info("Testing core modules...")
    try:
        from Feet_Measurements.image_processor import ModelLoader, FootMeasurementSystem
        from Feet_Measurements.geometric import QuadrilateralProcessor
        from Feet_Measurements.utils import GeometryUtils
        logger.info("✓ Core modules can be imported")
        return True
    except ImportError as e:
        logger.error(f"✗ Core module import failed: {e}")
        return False

def test_fastapi_app():
    """Test if FastAPI app can be imported."""
    logger.info("Testing FastAPI app...")
    try:
        from app import app
        logger.info("✓ FastAPI app can be imported")
        return True
    except ImportError as e:
        logger.error(f"✗ FastAPI app import failed: {e}")
        return False

def test_torch_device():
    """Test PyTorch device availability."""
    logger.info("Testing PyTorch device...")
    try:
        import torch
        if torch.cuda.is_available():
            device = torch.cuda.get_device_name(0)
            logger.info(f"✓ CUDA available: {device}")
        else:
            logger.info("✓ CPU mode available (CUDA not detected)")
        return True
    except Exception as e:
        logger.error(f"✗ PyTorch device test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("=" * 50)
    logger.info("EigenFit Setup Verification")
    logger.info("=" * 50)
    
    tests = [
        test_python_version,
        test_dependencies,
        test_directory_structure,
        test_core_modules,
        test_fastapi_app,
        test_torch_device,
        test_model_file
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} failed with exception: {e}")
        logger.info("-" * 30)
    
    logger.info("=" * 50)
    logger.info(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Setup is ready.")
        logger.info("You can now run:")
        logger.info("  uvicorn app:app --host 0.0.0.0 --port 8000")
        logger.info("  or")
        logger.info("  docker-compose up --build")
        return True
    else:
        logger.error("❌ Some tests failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
